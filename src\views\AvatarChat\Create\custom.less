.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: normal;
}

.create-digital-man-modal {
  min-height: 650px;
  display: grid;
  grid-template-columns: 1fr 1fr; /* 两列等宽 */
  font-family: PingFangSC, 'PingFang SC', sans-serif;
  cursor: default;
  border-top: #f0f1f2 solid 1px;

  .error-msg {
    height: 17px;
    font-size: 12px;
    font-weight: 400;
    line-height: 17px;
    color: #ff3c16;
  }

  .step-box {
    margin-top: 20px;
    margin-bottom: 12px;

    .step-index {
      display: inline-block;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      background: #d9e9ff;
      border-radius: 20px;
    }

    .step-title {
      display: inline-block;
      height: 20px;
      margin-left: 4px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #17181a;

      &.mr5 {
        margin-right: 5px;
      }
    }

    .info-icon {
      // position: relative;
      // top: 1px;
      font-size: 13px;
      color: #969799;
    }
  }

  .create {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;

    .format-tip {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .avatar-format-tip {
        margin-top: 20px;
        margin-bottom: 12px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #636466;
        line-height: 17px;
        text-align: left;
      }
    }

    .digital-human-list {
      width: 485px;
      height: 298px;
      padding: 16px 15px;
      overflow-y: hidden;
      background: #fff;
      border: 1px solid #f0f1f2;
      border-radius: 8px;

      .scroll-box {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(105px, 105px));
        gap: 10px; /* 列与列之间的间距 */
        height: 100%;
        overflow-y: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        .grid-item {
          width: 105px;
          height: 139px;
          background: linear-gradient(135deg, #fafafa 0%, #e6e6e6 100%);
          border-radius: 4px;

          &:hover {
            border: 1px solid #1777ff;
          }

          &.active {
            border: 2px solid #1777ff;

            img {
              border-radius: 2px;
            }
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 4px;
          }
        }
      }
    }

    .speakers-list {
      .speakers-btn {
        width: 130px;
        height: 32px;
        background: #f7f8fa;
        border-radius: 8px;

        .speakers-btn-text {
          width: 60px;
          height: 20px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #969799;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }

        .speakers-btn-icon {
          width: 6px;
          height: 11px;
          padding-left: 5px;
          font-weight: 500;
          color: #969799;
        }
      }
    }

    .compose-preview-btn {
      .flex-center;

      width: 91%;
      height: 36px;
      margin: 0 35px 20px 0;
      margin-top: auto;
      font-size: 14px;
      font-weight: 500;
      // color: #1777ff;
      background: #fff;
      // border: 1px solid #1777ff;
      border-radius: 6px;
      position: sticky;
      bottom: 0;
      z-index: 1;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: -20px; /* 与按钮保持一定间距 */
        height: 1px;
        width: 110%;
        background-color: #F0F1F2;; /* 柔和分隔线，更接近截图 */
      }
    }
  }

  .preview {
    flex: 1;
    // width: 577px;
    height: 98%;
    background: #f7f8fa;

    .preview-init {
      flex: 1;
      .flex-center;

      flex-direction: column;
      height: 100%;

      .preview-title {
        height: 20px;
        margin-top: 10px;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #333;
      }

      .preview-tip {

        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #969799;
        line-height: 24px;
        text-align: right;
        font-style: normal;
        margin-top: 18px;
        // height: 17px;
        // font-size: 12px;
        // font-weight: 400;
        // line-height: 17px;
        // color: #636466;
      }
    }

    .preview-train {
      display: flex;
      justify-content: center;
      width: 100%;
      padding-bottom: 35px;

      .train-img {
        width: 100%;
        height: 608px;
        object-fit: cover;
        margin-bottom: 21px;
      }

      .preview-content {
        width: 444px;

        .preview-footer {
          display: flex;
          justify-content: center;
          height: 50px;

          .preview-input {
            box-sizing: border-box;
            width: 317px;
            height: 36px;

            & > .ant-input {
              height: 27px;
            }

            &.error {
              .ant-input::placeholder,
              .ant-input-show-count-suffix {
                color: #ff3c16;
              }
            }

            .ant-input-affix-wrapper {
              background-color: #ffffff;
            }

            
          }

          :deep(.preview-input .ant-input-affix-wrapper .ant-input) {
              background: #ffffff !important;
            }

          .preview-btn {
            width: 111px;
            height: 36px;
            margin-left: 16px;
          }

          
          
        }
        .terms-agreement {
            display: flex;
            align-items: flex-start;
            font-size: 14px;
            color: #636466;


            .terms-link {
              color: inherit; // 继承父元素颜色，默认与"我已阅读并同意"相同
              text-decoration: none;
              margin-left: 4px;

              &:hover {
                opacity: 0.8;
                // text-decoration: underline;
              }
            }

            // 当checkbox选中时，链接显示为蓝色
            .ant-checkbox-wrapper.ant-checkbox-wrapper-checked .terms-link {
              color: #1890ff;
            }

            .ant-checkbox-wrapper {
              font-size: 14px;
              color: rgba(0,0,0,0.88);
            }
          }

      }
    }
  }
}
